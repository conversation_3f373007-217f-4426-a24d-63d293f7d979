<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Simple Watch - GoFlix</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background: #1a1a1a;
            color: white;
        }
        video {
            width: 100%;
            max-width: 600px;
            background: black;
        }
        .info {
            background: #333;
            padding: 15px;
            margin: 10px 0;
            border-radius: 5px;
        }
        button {
            padding: 10px 20px;
            margin: 5px;
            background: #007bff;
            color: white;
            border: none;
            border-radius: 5px;
            cursor: pointer;
        }
        button:hover {
            background: #0056b3;
        }
    </style>
</head>
<body>
    <h1>🎬 Simple Video Player</h1>
    
    <div class="info">
        <h2 id="videoTitle">Loading...</h2>
        <p id="videoDescription">Loading description...</p>
    </div>
    
    <div>
        <h2>Video Player</h2>
        <video id="video" controls>
            <source id="videoSource" src="" type="application/x-mpegURL">
            Your browser does not support HLS video playback.
        </video>
    </div>
    
    <div>
        <button onclick="loadVideo()">Load Video</button>
        <button onclick="testDirectStream()">Test Direct Stream</button>
        <a href="index.html">← Back to Gallery</a>
    </div>

    <script>
        // Get video ID from URL
        const urlParams = new URLSearchParams(window.location.search);
        const videoID = urlParams.get('id') || '1';
        
        console.log('Video ID:', videoID);
        
        async function loadVideoInfo() {
            try {
                const response = await fetch(`/api/videos/${videoID}`);
                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }
                
                const video = await response.json();
                console.log('Video data:', video);
                
                document.getElementById('videoTitle').textContent = video.title;
                document.getElementById('videoDescription').textContent = video.description;
                
                return video;
            } catch (error) {
                console.error('Error loading video info:', error);
                document.getElementById('videoTitle').textContent = 'Error loading video';
                document.getElementById('videoDescription').textContent = error.message;
            }
        }
        
        function loadVideo() {
            const video = document.getElementById('video');
            const source = document.getElementById('videoSource');
            const streamURL = `/stream/${videoID}/playlist.m3u8`;
            
            console.log('Loading stream:', streamURL);
            
            source.src = streamURL;
            video.load();
            
            video.addEventListener('loadstart', () => console.log('Video load started'));
            video.addEventListener('loadedmetadata', () => console.log('Video metadata loaded'));
            video.addEventListener('canplay', () => console.log('Video can start playing'));
            video.addEventListener('error', (e) => {
                console.error('Video error:', e);
                console.error('Video error details:', video.error);
            });
        }
        
        function testDirectStream() {
            const streamURL = `/stream/${videoID}/playlist.m3u8`;
            console.log('Testing direct access to:', streamURL);
            
            fetch(streamURL)
                .then(response => {
                    console.log('Stream response:', response.status, response.statusText);
                    console.log('Content-Type:', response.headers.get('Content-Type'));
                    return response.text();
                })
                .then(content => {
                    console.log('Stream content (first 500 chars):');
                    console.log(content.substring(0, 500));
                })
                .catch(error => {
                    console.error('Stream test failed:', error);
                });
        }
        
        // Auto-load on page load
        window.addEventListener('load', function() {
            console.log('Page loaded');
            loadVideoInfo();
            loadVideo();
        });
    </script>
</body>
</html>
