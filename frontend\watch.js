// frontend/watch.js

let hls = null;

document.addEventListener('DOMContentLoaded', () => {
    initPlayer();
});

/**
 * Initialize the video player and load video data
 */
async function initPlayer() {
    // Get video ID from URL query parameter
    const urlParams = new URLSearchParams(window.location.search);
    const videoID = urlParams.get('id');
    
    if (!videoID) {
        showError('No video ID provided');
        return;
    }
    
    try {
        // Fetch video metadata
        const video = await fetchVideoMetadata(videoID);
        
        if (!video) {
            showError('Video not found');
            return;
        }
        
        // Update page with video information
        updateVideoInfo(video);
        
        // Initialize HLS player
        setupHLSPlayer(videoID);
        
        // TODO: Expansion Point - Track video view/start watching
        // trackVideoView(videoID);
        
        // TODO: Expansion Point - Resume from last watched position
        // const savedPosition = getSavedPosition(videoID);
        // if (savedPosition) video.currentTime = savedPosition;
        
    } catch (error) {
        console.error('Error initializing player:', error);
        showError('Failed to load video');
    }
}

/**
 * Fetch video metadata from the API
 * @param {string} videoID - The video ID
 * @returns {Promise<Object>} The video object
 */
async function fetchVideoMetadata(videoID) {
    const response = await fetch(`/api/videos/${videoID}`);
    
    if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
    }
    
    return await response.json();
}

/**
 * Update the page with video information
 * @param {Object} video - The video object
 */
function updateVideoInfo(video) {
    document.getElementById('video-title').textContent = video.title;
    document.getElementById('video-description').textContent = 
        video.description || 'No description available.';
    
    // Update page title
    document.title = `${video.title} - GoFlix`;
}

/**
 * Set up the HLS player using hls.js
 * @param {string} videoID - The video ID
 */
function setupHLSPlayer(videoID) {
    const video = document.getElementById('video');
    const loadingElement = document.getElementById('loading');
    const streamURL = `/stream/${videoID}/playlist.m3u8`;
    
    // Check if HLS is supported
    if (Hls.isSupported()) {
        console.log('HLS.js is supported, initializing...');
        
        hls = new Hls({
            debug: false,
            enableWorker: true,
            lowLatencyMode: false,
            backBufferLength: 90
        });
        
        // TODO: Expansion Point - Configure HLS.js for adaptive bitrate
        // maxBufferLength: 30,
        // maxMaxBufferLength: 600,
        // maxBufferSize: 60 * 1000 * 1000,
        
        hls.loadSource(streamURL);
        hls.attachMedia(video);
        
        // Event: Manifest parsed (playlist loaded successfully)
        hls.on(Hls.Events.MANIFEST_PARSED, function() {
            console.log('HLS manifest parsed, ready to play');
            loadingElement.style.display = 'none';
            
            // Auto-play (might be blocked by browser)
            video.play().catch(e => {
                console.log('Auto-play prevented:', e);
                // User will need to click play manually
            });
            
            // TODO: Expansion Point - Show quality levels available
            // const levels = hls.levels;
            // displayQualitySelector(levels);
        });
        
        // Event: Error handling
        hls.on(Hls.Events.ERROR, function(event, data) {
            console.error('HLS error:', data);
            
            if (data.fatal) {
                switch(data.type) {
                    case Hls.ErrorTypes.NETWORK_ERROR:
                        console.error('Fatal network error, trying to recover...');
                        hls.startLoad();
                        break;
                    case Hls.ErrorTypes.MEDIA_ERROR:
                        console.error('Fatal media error, trying to recover...');
                        hls.recoverMediaError();
                        break;
                    default:
                        console.error('Fatal error, cannot recover');
                        showError('Playback failed. Please refresh and try again.');
                        hls.destroy();
                        break;
                }
            }
        });
        
        // TODO: Expansion Point - Track playback progress for resume feature
        // video.addEventListener('timeupdate', () => {
        //     savePlaybackPosition(videoID, video.currentTime);
        // });
        
        // TODO: Expansion Point - Add quality level change listener
        // hls.on(Hls.Events.LEVEL_SWITCHED, function(event, data) {
        //     console.log('Quality changed to level', data.level);
        // });
        
    } else if (video.canPlayType('application/vnd.apple.mpegurl')) {
        // Native HLS support (Safari, iOS)
        console.log('Native HLS support detected');
        
        video.src = streamURL;
        
        video.addEventListener('loadedmetadata', function() {
            console.log('Video metadata loaded');
            loadingElement.style.display = 'none';
            
            video.play().catch(e => {
                console.log('Auto-play prevented:', e);
            });
        });
        
        video.addEventListener('error', function() {
            console.error('Video error:', video.error);
            showError('Playback failed. Please refresh and try again.');
        });
        
    } else {
        showError('HLS is not supported in this browser. Please try Chrome, Firefox, or Safari.');
    }
}

/**
 * Show error message and hide loading
 * @param {string} message - Error message to display
 */
function showError(message) {
    const loadingElement = document.getElementById('loading');
    const errorElement = document.getElementById('error');
    
    loadingElement.style.display = 'none';
    errorElement.style.display = 'flex';
    errorElement.querySelector('p').textContent = message;
}

/**
 * Cleanup when page is unloaded
 */
window.addEventListener('beforeunload', () => {
    if (hls) {
        hls.destroy();
    }
});

// TODO: Expansion Point - Implement keyboard shortcuts
// document.addEventListener('keydown', (e) => {
//     const video = document.getElementById('video');
//     switch(e.key) {
//         case ' ': // Space = play/pause
//         case 'ArrowLeft': // Left = rewind 10s
//         case 'ArrowRight': // Right = forward 10s
//         case 'f': // F = fullscreen
//     }
// });

// TODO: Expansion Point - Add picture-in-picture support
// function enablePiP() {
//     const video = document.getElementById('video');
//     if (document.pictureInPictureEnabled) {
//         video.requestPictureInPicture();
//     }
// }

// TODO: Expansion Point - Track watch time for analytics
// function trackWatchTime(videoID, duration) { ... }