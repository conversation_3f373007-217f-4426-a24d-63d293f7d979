/* https://www.youtube.com/@naftorium */

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background-color: #141414;
    color: #ffffff;
    line-height: 1.6;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

/* Header Styles */
header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    padding: 2rem 0;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.3);
}

.logo {
    font-size: 2.5rem;
    font-weight: bold;
    margin-bottom: 0.5rem;
}

.tagline {
    font-size: 1.1rem;
    opacity: 0.9;
}

/* Main Content */
main {
    padding: 3rem 0;
    min-height: 60vh;
}

main h2 {
    margin-bottom: 2rem;
    font-size: 1.8rem;
}

/* Video Grid */
.video-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
    gap: 2rem;
}

/* TODO: Expansion Point - Add filters/sorting controls above the grid */

/* Video Card */
.video-card {
    background-color: #1f1f1f;
    border-radius: 8px;
    overflow: hidden;
    transition: transform 0.3s ease, box-shadow 0.3s ease;
    cursor: pointer;
    text-decoration: none;
    color: inherit;
    display: block;
}

.video-card:hover {
    transform: translateY(-8px);
    box-shadow: 0 12px 24px rgba(0, 0, 0, 0.5);
}

.video-card:hover .thumbnail {
    opacity: 0.8;
}

.thumbnail {
    width: 100%;
    height: 200px;
    object-fit: cover;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 3rem;
    transition: opacity 0.3s ease;
}

.thumbnail.placeholder {
    background: linear-gradient(135deg, #434343 0%, #000000 100%);
}

.video-info {
    padding: 1.5rem;
}

.video-title {
    font-size: 1.2rem;
    font-weight: 600;
    margin-bottom: 0.5rem;
    color: #ffffff;
}

.video-description {
    font-size: 0.9rem;
    color: #b3b3b3;
    line-height: 1.4;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
}

/* Loading State */
.loading {
    grid-column: 1 / -1;
    text-align: center;
    padding: 3rem;
    font-size: 1.2rem;
    color: #b3b3b3;
}

/* Error State */
.error {
    grid-column: 1 / -1;
    text-align: center;
    padding: 3rem;
    color: #e50914;
    font-size: 1.2rem;
}

/* Footer */
footer {
    background-color: #0a0a0a;
    padding: 2rem 0;
    margin-top: 4rem;
    text-align: center;
    color: #b3b3b3;
}

/* Responsive Design */
@media (max-width: 768px) {
    .video-grid {
        grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
        gap: 1.5rem;
    }
    
    .logo {
        font-size: 2rem;
    }
    
    main h2 {
        font-size: 1.5rem;
    }
}

@media (max-width: 480px) {
    .video-grid {
        grid-template-columns: 1fr;
    }
}

/* TODO: Expansion Point - Add dark/light theme toggle styles */
/* TODO: Expansion Point - Add animations for card entry */