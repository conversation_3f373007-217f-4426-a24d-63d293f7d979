// https://www.youtube.com/@naftorium

package main

// Video represents a video in our database
type Video struct {
	ID             int    `json:"id"`
	Title          string `json:"title"`
	Description    string `json:"description"`
	ThumbnailPath  string `json:"thumbnail_path"`
	SourceFilename string `json:"source_filename"`
}

// TODO: Expansion Point - Add fields for duration, upload_date, view_count
// TODO: Expansion Point - Add category/genre field for filtering
// TODO: Expansion Point - Add user_id field for multi-user support