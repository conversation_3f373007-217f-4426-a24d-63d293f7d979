// https://www.youtube.com/@naftorium


document.addEventListener('DOMContentLoaded', () => {
    loadVideos();
});

/**
 * Fetches videos from the API and renders them to the page
 */
async function loadVideos() {
    const gallery = document.getElementById('video-gallery');
    
    try {
        const response = await fetch('/api/videos');
        
        if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
        }
        
        const videos = await response.json();
        
        // Clear loading message
        gallery.innerHTML = '';
        
        if (videos.length === 0) {
            gallery.innerHTML = '<div class="loading">No videos available yet.</div>';
            return;
        }
        
        // Render each video as a card
        videos.forEach(video => {
            const card = createVideoCard(video);
            gallery.appendChild(card);
        });
        
        // TODO: Expansion Point - Implement infinite scroll or pagination
        // TODO: Expansion Point - Add skeleton loaders while images load
        
    } catch (error) {
        console.error('Error loading videos:', error);
        gallery.innerHTML = `
            <div class="error">
                ⚠️ Failed to load videos. Please try again later.
            </div>
        `;
    }
}

/**
 * Creates a video card element
 * @param {Object} video - The video object from the API
 * @returns {HTMLElement} The video card element
 */
function createVideoCard(video) {
    const card = document.createElement('a');
    card.href = `watch.html?id=${video.id}`;
    card.className = 'video-card';
    
    // Create thumbnail
    const thumbnail = document.createElement('div');
    thumbnail.className = 'thumbnail placeholder';
    thumbnail.textContent = '🎬'; // Default placeholder icon
    
    // TODO: Expansion Point - Actually load and display thumbnail images
    // if (video.thumbnail_path) {
    //     const img = document.createElement('img');
    //     img.src = video.thumbnail_path;
    //     img.alt = video.title;
    //     img.className = 'thumbnail';
    //     thumbnail.replaceWith(img);
    // }
    
    // Create video info section
    const info = document.createElement('div');
    info.className = 'video-info';
    
    const title = document.createElement('h3');
    title.className = 'video-title';
    title.textContent = video.title;
    
    const description = document.createElement('p');
    description.className = 'video-description';
    description.textContent = video.description || 'No description available.';
    
    info.appendChild(title);
    info.appendChild(description);
    
    card.appendChild(thumbnail);
    card.appendChild(info);
    
    // TODO: Expansion Point - Add video duration badge
    // TODO: Expansion Point - Add "Continue Watching" progress bar
    
    return card;
}

// TODO: Expansion Point - Add search functionality
// function searchVideos(query) { ... }

// TODO: Expansion Point - Add category filtering
// function filterByCategory(category) { ... }