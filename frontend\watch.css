/* frontend/watch.css */

/* Player Container */
.player-container {
    position: relative;
    width: 100%;
    max-width: 1200px;
    margin: 2rem auto;
    background-color: #000;
    border-radius: 8px;
    overflow: hidden;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.5);
}

.video-player {
    width: 100%;
    height: auto;
    display: block;
    aspect-ratio: 16 / 9;
}

/* Loading State */
.player-loading {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    background-color: #000;
    color: #fff;
}

.spinner {
    border: 4px solid rgba(255, 255, 255, 0.1);
    border-top: 4px solid #667eea;
    border-radius: 50%;
    width: 50px;
    height: 50px;
    animation: spin 1s linear infinite;
    margin-bottom: 1rem;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Error State */
.player-error {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    background-color: #000;
    color: #e50914;
    padding: 2rem;
    text-align: center;
}

.player-error p {
    font-size: 1.2rem;
    margin-bottom: 1.5rem;
}

/* Video Details */
.video-details {
    margin: 2rem auto;
    max-width: 1200px;
}

.video-title-large {
    font-size: 2rem;
    margin-bottom: 1rem;
    color: #fff;
}

.video-description-full {
    font-size: 1.1rem;
    line-height: 1.8;
    color: #b3b3b3;
    margin-bottom: 2rem;
}

/* Actions */
.actions {
    margin: 2rem auto;
    max-width: 1200px;
    display: flex;
    gap: 1rem;
}

.btn-secondary, .btn-back {
    display: inline-block;
    padding: 0.75rem 1.5rem;
    background-color: #333;
    color: #fff;
    text-decoration: none;
    border-radius: 4px;
    border: none;
    font-size: 1rem;
    cursor: pointer;
    transition: background-color 0.3s ease;
}

.btn-secondary:hover, .btn-back:hover {
    background-color: #555;
}

.logo-link {
    text-decoration: none;
    color: inherit;
}

/* Responsive */
@media (max-width: 768px) {
    .video-title-large {
        font-size: 1.5rem;
    }
    
    .video-description-full {
        font-size: 1rem;
    }
    
    .player-container {
        margin: 1rem 0;
        border-radius: 0;
    }
}

/* TODO: Expansion Point - Add styles for custom video controls */
/* TODO: Expansion Point - Add styles for playback speed selector */
/* TODO: Expansion Point - Add styles for quality selector */