<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Debug - GoFlix</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1000px;
            margin: 0 auto;
            padding: 20px;
            background: #1a1a1a;
            color: white;
        }
        .section {
            background: #333;
            padding: 15px;
            margin: 10px 0;
            border-radius: 5px;
        }
        .success { border-left: 5px solid #4CAF50; }
        .error { border-left: 5px solid #f44336; }
        .warning { border-left: 5px solid #ff9800; }
        .info { border-left: 5px solid #2196F3; }
        
        button {
            padding: 8px 16px;
            margin: 5px;
            background: #007bff;
            color: white;
            border: none;
            border-radius: 3px;
            cursor: pointer;
        }
        button:hover { background: #0056b3; }
        
        pre {
            background: #222;
            padding: 10px;
            border-radius: 3px;
            overflow-x: auto;
            white-space: pre-wrap;
        }
        
        video {
            width: 100%;
            max-width: 500px;
            background: black;
        }
    </style>
</head>
<body>
    <h1>🔧 GoFlix Debug Console</h1>
    
    <div class="section info">
        <h2>🎯 Quick Actions</h2>
        <button onclick="runAllTests()">Run All Tests</button>
        <button onclick="clearResults()">Clear Results</button>
        <button onclick="testVideoPlayback()">Test Video Playback</button>
    </div>
    
    <div id="results"></div>
    
    <div class="section">
        <h2>📺 Video Player Test</h2>
        <video id="testVideo" controls>
            <source id="videoSource" type="application/x-mpegURL">
            Your browser does not support the video tag.
        </video>
        <div>
            <button onclick="loadHLSVideo()">Load HLS Video</button>
            <button onclick="loadNativeVideo()">Load Native Video</button>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/hls.js@latest"></script>
    <script>
        const results = document.getElementById('results');
        
        function addResult(title, content, type = 'info') {
            const div = document.createElement('div');
            div.className = `section ${type}`;
            div.innerHTML = `<h3>${title}</h3><pre>${content}</pre>`;
            results.appendChild(div);
        }
        
        function clearResults() {
            results.innerHTML = '';
        }
        
        async function runAllTests() {
            clearResults();
            addResult('🚀 Starting Diagnostic Tests', 'Running comprehensive tests...', 'info');
            
            await testBrowserInfo();
            await testHLSLibrary();
            await testVideoAPI();
            await testStreamEndpoint();
            await testNetworkConnectivity();
            
            addResult('✅ All Tests Complete', 'Check results above for any issues', 'success');
        }
        
        function testBrowserInfo() {
            const info = {
                userAgent: navigator.userAgent,
                platform: navigator.platform,
                language: navigator.language,
                cookieEnabled: navigator.cookieEnabled,
                onLine: navigator.onLine,
                url: window.location.href,
                protocol: window.location.protocol,
                host: window.location.host
            };
            
            addResult('🌐 Browser Information', JSON.stringify(info, null, 2), 'info');
        }
        
        function testHLSLibrary() {
            let result = '';
            let type = 'success';
            
            try {
                if (typeof Hls === 'undefined') {
                    result = '❌ HLS.js library not loaded!\nThe CDN might be blocked or failed to load.';
                    type = 'error';
                } else {
                    result = `✅ HLS.js library loaded successfully\n`;
                    result += `Version: ${Hls.version || 'unknown'}\n`;
                    result += `Supported: ${Hls.isSupported()}\n`;
                    
                    if (Hls.isSupported()) {
                        result += `✅ HLS.js is supported in this browser`;
                    } else {
                        result += `⚠️ HLS.js is not supported in this browser`;
                        type = 'warning';
                    }
                }
            } catch (error) {
                result = `❌ Error testing HLS.js: ${error.message}`;
                type = 'error';
            }
            
            addResult('📦 HLS.js Library Test', result, type);
        }
        
        async function testVideoAPI() {
            const videoID = new URLSearchParams(window.location.search).get('id') || '1';
            const apiURL = `/api/videos/${videoID}`;
            
            try {
                const response = await fetch(apiURL);
                let result = `URL: ${apiURL}\n`;
                result += `Status: ${response.status} ${response.statusText}\n`;
                result += `Content-Type: ${response.headers.get('Content-Type')}\n\n`;
                
                if (response.ok) {
                    const data = await response.json();
                    result += `✅ API Response:\n${JSON.stringify(data, null, 2)}`;
                    addResult('📡 Video API Test', result, 'success');
                } else {
                    result += `❌ API returned error status`;
                    addResult('📡 Video API Test', result, 'error');
                }
            } catch (error) {
                addResult('📡 Video API Test', `❌ API request failed: ${error.message}`, 'error');
            }
        }
        
        async function testStreamEndpoint() {
            const videoID = new URLSearchParams(window.location.search).get('id') || '1';
            const streamURL = `/stream/${videoID}/playlist.m3u8`;
            
            try {
                const response = await fetch(streamURL);
                let result = `URL: ${streamURL}\n`;
                result += `Status: ${response.status} ${response.statusText}\n`;
                result += `Content-Type: ${response.headers.get('Content-Type')}\n`;
                result += `Content-Length: ${response.headers.get('Content-Length')}\n\n`;
                
                if (response.ok) {
                    const content = await response.text();
                    result += `✅ Stream accessible\n\n`;
                    result += `Playlist content (first 500 chars):\n${content.substring(0, 500)}`;
                    if (content.length > 500) result += '\n... (truncated)';
                    addResult('🎬 Stream Endpoint Test', result, 'success');
                } else {
                    result += `❌ Stream not accessible`;
                    addResult('🎬 Stream Endpoint Test', result, 'error');
                }
            } catch (error) {
                addResult('🎬 Stream Endpoint Test', `❌ Stream request failed: ${error.message}`, 'error');
            }
        }
        
        async function testNetworkConnectivity() {
            const tests = [
                { name: 'Local Server', url: '/hello' },
                { name: 'HLS.js CDN', url: 'https://cdn.jsdelivr.net/npm/hls.js@latest' },
                { name: 'Google DNS', url: 'https://8.8.8.8' }
            ];
            
            let result = '';
            
            for (const test of tests) {
                try {
                    const start = Date.now();
                    const response = await fetch(test.url, { method: 'HEAD', mode: 'no-cors' });
                    const time = Date.now() - start;
                    result += `✅ ${test.name}: ${time}ms\n`;
                } catch (error) {
                    result += `❌ ${test.name}: ${error.message}\n`;
                }
            }
            
            addResult('🌐 Network Connectivity Test', result, 'info');
        }
        
        function testVideoPlayback() {
            const video = document.getElementById('testVideo');
            const videoID = new URLSearchParams(window.location.search).get('id') || '1';
            
            addResult('🎥 Video Playback Test', 'Starting video playback test...', 'info');
            
            // Test native HLS support first
            if (video.canPlayType('application/vnd.apple.mpegurl')) {
                loadNativeVideo();
            } else if (typeof Hls !== 'undefined' && Hls.isSupported()) {
                loadHLSVideo();
            } else {
                addResult('🎥 Video Playback Result', '❌ No HLS support available', 'error');
            }
        }
        
        function loadNativeVideo() {
            const video = document.getElementById('testVideo');
            const source = document.getElementById('videoSource');
            const videoID = new URLSearchParams(window.location.search).get('id') || '1';
            const streamURL = `/stream/${videoID}/playlist.m3u8`;
            
            addResult('🎥 Native HLS Test', `Attempting native HLS playback: ${streamURL}`, 'info');
            
            source.src = streamURL;
            video.load();
            
            video.addEventListener('loadstart', () => {
                addResult('🎥 Native HLS Event', '✅ Load started', 'success');
            });
            
            video.addEventListener('loadedmetadata', () => {
                addResult('🎥 Native HLS Event', '✅ Metadata loaded', 'success');
            });
            
            video.addEventListener('canplay', () => {
                addResult('🎥 Native HLS Event', '✅ Can play - attempting autoplay', 'success');
                video.play().catch(e => {
                    addResult('🎥 Native HLS Event', `⚠️ Autoplay blocked: ${e.message}`, 'warning');
                });
            });
            
            video.addEventListener('error', (e) => {
                addResult('🎥 Native HLS Event', `❌ Video error: ${video.error?.message || 'Unknown error'}`, 'error');
            });
        }
        
        function loadHLSVideo() {
            if (typeof Hls === 'undefined') {
                addResult('🎥 HLS.js Test', '❌ HLS.js not available', 'error');
                return;
            }
            
            const video = document.getElementById('testVideo');
            const videoID = new URLSearchParams(window.location.search).get('id') || '1';
            const streamURL = `/stream/${videoID}/playlist.m3u8`;
            
            addResult('🎥 HLS.js Test', `Attempting HLS.js playback: ${streamURL}`, 'info');
            
            const hls = new Hls({ debug: true });
            
            hls.loadSource(streamURL);
            hls.attachMedia(video);
            
            hls.on(Hls.Events.MANIFEST_PARSED, () => {
                addResult('🎥 HLS.js Event', '✅ Manifest parsed - attempting autoplay', 'success');
                video.play().catch(e => {
                    addResult('🎥 HLS.js Event', `⚠️ Autoplay blocked: ${e.message}`, 'warning');
                });
            });
            
            hls.on(Hls.Events.ERROR, (event, data) => {
                const message = `❌ HLS.js error: ${data.type} - ${data.details}`;
                addResult('🎥 HLS.js Event', message, data.fatal ? 'error' : 'warning');
            });
        }
        
        // Auto-run tests on page load
        window.addEventListener('load', () => {
            setTimeout(runAllTests, 500);
        });
    </script>
</body>
</html>
