<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>HLS Test - GoFlix</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background: #1a1a1a;
            color: white;
        }
        video {
            width: 100%;
            max-width: 600px;
            background: black;
        }
        .log {
            background: #333;
            padding: 10px;
            margin: 10px 0;
            border-radius: 5px;
            font-family: monospace;
            white-space: pre-wrap;
        }
        button {
            padding: 10px 20px;
            margin: 5px;
            background: #007bff;
            color: white;
            border: none;
            border-radius: 5px;
            cursor: pointer;
        }
        button:hover {
            background: #0056b3;
        }
    </style>
</head>
<body>
    <h1>🎬 HLS.js Test Page</h1>
    
    <div>
        <h2>Test Controls</h2>
        <button onclick="testHLSLibrary()">Test HLS.js Library</button>
        <button onclick="testStreamURL()">Test Stream URL</button>
        <button onclick="testVideoAPI()">Test Video API</button>
        <button onclick="initTestPlayer()">Initialize Player</button>
    </div>
    
    <div>
        <h2>Video Player</h2>
        <video id="testVideo" controls>
            Your browser does not support the video tag.
        </video>
    </div>
    
    <div>
        <h2>Debug Log</h2>
        <div id="log" class="log"></div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/hls.js@latest"></script>
    <script>
        const log = document.getElementById('log');
        
        function addLog(message) {
            const timestamp = new Date().toLocaleTimeString();
            log.textContent += `[${timestamp}] ${message}\n`;
            log.scrollTop = log.scrollHeight;
            console.log(message);
        }
        
        function testHLSLibrary() {
            addLog('🔍 Testing HLS.js library...');
            
            if (typeof Hls === 'undefined') {
                addLog('❌ HLS.js library not loaded!');
                return;
            }
            
            addLog('✅ HLS.js library loaded successfully');
            addLog(`📦 HLS.js version: ${Hls.version || 'unknown'}`);
            
            if (Hls.isSupported()) {
                addLog('✅ HLS.js is supported in this browser');
            } else {
                addLog('❌ HLS.js is not supported in this browser');
            }
        }
        
        async function testStreamURL() {
            const streamURL = '/stream/1/playlist.m3u8';
            addLog(`🔗 Testing stream URL: ${streamURL}`);
            
            try {
                const response = await fetch(streamURL);
                addLog(`📡 Response status: ${response.status} ${response.statusText}`);
                addLog(`📄 Content-Type: ${response.headers.get('Content-Type')}`);
                
                if (response.ok) {
                    const content = await response.text();
                    addLog(`📝 Playlist content (first 200 chars):`);
                    addLog(content.substring(0, 200) + '...');
                } else {
                    addLog('❌ Stream URL returned error');
                }
            } catch (error) {
                addLog(`❌ Failed to fetch stream URL: ${error.message}`);
            }
        }
        
        async function testVideoAPI() {
            const apiURL = '/api/videos/1';
            addLog(`🔗 Testing video API: ${apiURL}`);
            
            try {
                const response = await fetch(apiURL);
                addLog(`📡 Response status: ${response.status} ${response.statusText}`);
                
                if (response.ok) {
                    const video = await response.json();
                    addLog(`📹 Video data: ${JSON.stringify(video, null, 2)}`);
                } else {
                    addLog('❌ Video API returned error');
                }
            } catch (error) {
                addLog(`❌ Failed to fetch video API: ${error.message}`);
            }
        }
        
        function initTestPlayer() {
            addLog('🎥 Initializing test player...');
            
            if (typeof Hls === 'undefined') {
                addLog('❌ HLS.js not available');
                return;
            }
            
            const video = document.getElementById('testVideo');
            const streamURL = '/stream/1/playlist.m3u8';
            
            if (Hls.isSupported()) {
                const hls = new Hls({
                    debug: true
                });
                
                hls.loadSource(streamURL);
                hls.attachMedia(video);
                
                hls.on(Hls.Events.MANIFEST_PARSED, function() {
                    addLog('✅ HLS manifest parsed successfully');
                    video.play().then(() => {
                        addLog('✅ Video started playing');
                    }).catch(e => {
                        addLog(`⚠️ Auto-play prevented: ${e.message}`);
                    });
                });
                
                hls.on(Hls.Events.ERROR, function(event, data) {
                    addLog(`❌ HLS error: ${data.type} - ${data.details}`);
                    if (data.fatal) {
                        addLog('💀 Fatal error occurred');
                    }
                });
                
                addLog('🔄 HLS player initialized');
            } else {
                addLog('❌ HLS not supported, trying native playback');
                video.src = streamURL;
            }
        }
        
        // Auto-run tests on page load
        window.addEventListener('load', function() {
            addLog('🚀 Page loaded, running automatic tests...');
            testHLSLibrary();
            setTimeout(testVideoAPI, 1000);
            setTimeout(testStreamURL, 2000);
        });
    </script>
</body>
</html>
