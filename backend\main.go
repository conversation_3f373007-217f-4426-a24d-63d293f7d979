// https://www.youtube.com/@naftorium 

// backend/main.go
package main

import (
	"encoding/json"
	"fmt"
	"log"
	"net/http"
	"path/filepath"
	"strings"
)

func main() {
	// Initialize the database
	if err := InitDatabase(); err != nil {
		log.Fatal("Failed to initialize database:", err)
	}
	defer CloseDatabase()
	
	// Seed the database with sample data
	if err := SeedDatabase(); err != nil {
		log.Fatal("Failed to seed database:", err)
	}
	
	// Serve static files from the frontend directory
	frontendPath := filepath.Join("..", "frontend")
	fs := http.FileServer(http.Dir(frontendPath))
	http.Handle("/", fs)
	
	// API routes
	http.HandleFunc("/api/videos", handleGetVideos)
	http.HandleFunc("/api/videos/", handleGetVideoByID) // Note the trailing slash
	
	// Test route
	http.HandleFunc("/hello", handleHello)
	
	// TODO: Expansion Point - Add API versioning (e.g., /api/v1/videos)
	// TODO: Expansion Point - Add rate limiting middleware
	
	port := ":8080"
	fmt.Printf("🚀 GoFlix server starting on http://localhost%s\n", port)
	fmt.Printf("📁 Serving frontend from: %s\n", frontendPath)
	fmt.Printf("🌐 Access from other devices: http://<your-local-ip>%s\n", port)
	
	if err := http.ListenAndServe(port, nil); err != nil {
		log.Fatal("Server failed to start:", err)
	}
}

func handleHello(w http.ResponseWriter, r *http.Request) {
	w.Header().Set("Content-Type", "text/plain")
	fmt.Fprintf(w, "Hello from GoFlix! 🎬")
}

// handleGetVideos returns all videos as JSON
func handleGetVideos(w http.ResponseWriter, r *http.Request) {
	if r.Method != http.MethodGet {
		http.Error(w, "Method not allowed", http.StatusMethodNotAllowed)
		return
	}
	
	videos, err := GetAllVideos()
	if err != nil {
		log.Println("Error fetching videos:", err)
		http.Error(w, "Internal server error", http.StatusInternalServerError)
		return
	}
	
	w.Header().Set("Content-Type", "application/json")
	json.NewEncoder(w).Encode(videos)
	
	// TODO: Expansion Point - Add filtering query parameters (e.g., ?genre=action)
	// TODO: Expansion Point - Add search functionality (e.g., ?q=bunny)
}

// handleGetVideoByID returns a single video by ID
func handleGetVideoByID(w http.ResponseWriter, r *http.Request) {
	if r.Method != http.MethodGet {
		http.Error(w, "Method not allowed", http.StatusMethodNotAllowed)
		return
	}
	
	// Extract ID from URL path (/api/videos/1 -> "1")
	pathParts := strings.Split(strings.Trim(r.URL.Path, "/"), "/")
	if len(pathParts) < 3 {
		http.Error(w, "Video ID required", http.StatusBadRequest)
		return
	}
	
	videoID := pathParts[2]
	
	video, err := GetVideoByID(videoID)
	if err != nil {
		log.Println("Error fetching video:", err)
		http.Error(w, "Internal server error", http.StatusInternalServerError)
		return
	}
	
	if video == nil {
		http.Error(w, "Video not found", http.StatusNotFound)
		return
	}
	
	w.Header().Set("Content-Type", "application/json")
	json.NewEncoder(w).Encode(video)
}