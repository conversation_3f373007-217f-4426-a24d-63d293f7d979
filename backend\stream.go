// https://www.youtube.com/@naftorium 

// backend/stream.go
package main

import (
	"fmt"
	"log"
	"net/http"
	"os"
	"os/exec"
	"path/filepath"
	"strings"
)

// handleStream manages HLS stream generation and serving
func handleStream(w http.ResponseWriter, r *http.Request) {
	// Extract video ID and filename from URL
	// Expected format: /stream/{videoID}/{filename}
	pathParts := strings.Split(strings.Trim(r.URL.Path, "/"), "/")
	
	if len(pathParts) < 3 {
		http.Error(w, "Invalid stream URL", http.StatusBadRequest)
		return
	}
	
	videoID := pathParts[1]
	filename := pathParts[2]
	
	// Validate filename to prevent directory traversal attacks
	if strings.Contains(filename, "..") || strings.Contains(filename, "/") {
		http.Error(w, "Invalid filename", http.StatusBadRequest)
		return
	}
	
	// TODO: Expansion Point - Add authentication check here
	// if !isUserAuthorized(r, videoID) { ... }
	
	// Get video metadata from database
	video, err := GetVideoByID(videoID)
	if err != nil {
		log.Println("Error fetching video:", err)
		http.Error(w, "Internal server error", http.StatusInternalServerError)
		return
	}
	
	if video == nil {
		http.Error(w, "Video not found", http.StatusNotFound)
		return
	}
	
	// Construct paths
	sourceVideoPath := filepath.Join("..", "videos", video.SourceFilename)
	transcodedDir := filepath.Join("..", "transcoded", videoID)
	playlistPath := filepath.Join(transcodedDir, "playlist.m3u8")
	requestedFilePath := filepath.Join(transcodedDir, filename)
	
	// Check if source video exists
	if _, err := os.Stat(sourceVideoPath); os.IsNotExist(err) {
		log.Printf("Source video not found: %s", sourceVideoPath)
		http.Error(w, "Source video not found", http.StatusNotFound)
		return
	}
	
	// Generate HLS stream if it doesn't exist
	if _, err := os.Stat(playlistPath); os.IsNotExist(err) {
		log.Printf("Generating HLS stream for video ID %s", videoID)
		
		if err := generateHLS(sourceVideoPath, transcodedDir); err != nil {
			log.Println("Error generating HLS:", err)
			http.Error(w, "Failed to generate stream", http.StatusInternalServerError)
			return
		}
		
		log.Printf("✅ HLS stream generated successfully for video ID %s", videoID)
	}
	
	// Serve the requested file (playlist.m3u8 or .ts segment)
	if _, err := os.Stat(requestedFilePath); os.IsNotExist(err) {
		http.Error(w, "Stream file not found", http.StatusNotFound)
		return
	}
	
	// Set appropriate content type
	if strings.HasSuffix(filename, ".m3u8") {
		w.Header().Set("Content-Type", "application/vnd.apple.mpegurl")
	} else if strings.HasSuffix(filename, ".ts") {
		w.Header().Set("Content-Type", "video/mp2t")
	}
	
	// Enable CORS for HLS streaming
	w.Header().Set("Access-Control-Allow-Origin", "*")
	
	// TODO: Expansion Point - Add caching headers for .ts segments
	// w.Header().Set("Cache-Control", "public, max-age=31536000")
	
	http.ServeFile(w, r, requestedFilePath)
}

// generateHLS transcodes a video to HLS format using FFMPEG
func generateHLS(sourceVideo, outputDir string) error {
	// Create output directory if it doesn't exist
	if err := os.MkdirAll(outputDir, 0755); err != nil {
		return fmt.Errorf("failed to create output directory: %w", err)
	}
	
	playlistPath := filepath.Join(outputDir, "playlist.m3u8")
	segmentPattern := filepath.Join(outputDir, "segment%d.ts")
	
	// FFMPEG command for HLS transcoding
	// -i: input file
	// -profile:v baseline: H.264 baseline profile for compatibility
	// -level 3.0: H.264 level for compatibility
	// -start_number 0: start segment numbering at 0
	// -hls_time 10: each segment is 10 seconds
	// -hls_list_size 0: keep all segments in playlist
	// -f hls: output format is HLS
	args := []string{
		"-i", sourceVideo,
		"-profile:v", "baseline",
		"-level", "3.0",
		"-start_number", "0",
		"-hls_time", "10",
		"-hls_list_size", "0",
		"-f", "hls",
		"-hls_segment_filename", segmentPattern,
		playlistPath,
	}
	
	// TODO: Expansion Point - Add multiple quality levels (adaptive bitrate)
	// Example for 720p:
	// "-vf", "scale=-2:720",
	// "-b:v", "2500k",
	// "-maxrate", "2675k",
	// "-bufsize", "3750k",
	
	// TODO: Expansion Point - Add audio quality settings
	// "-c:a", "aac",
	// "-b:a", "128k",
	
	// TODO: Expansion Point - Add progress callback
	// Could parse FFMPEG output to show transcoding progress to user
	
	cmd := exec.Command("ffmpeg", args...)
	
	// Capture output for debugging
	output, err := cmd.CombinedOutput()
	if err != nil {
		return fmt.Errorf("ffmpeg error: %w\nOutput: %s", err, string(output))
	}
	
	return nil
}

// TODO: Expansion Point - Add func