<!-- https://www.youtube.com/@naftorium -->

<!-- frontend/watch.html -->
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Watch Video - GoFlix</title>
    <link rel="stylesheet" href="style.css">
    <link rel="stylesheet" href="watch.css">
</head>
<body>
    <header>
        <div class="container">
            <a href="index.html" class="logo-link">
                <h1 class="logo">🎬 GoFlix</h1>
            </a>
        </div>
    </header>
    
    <main class="container">
        <div class="player-container">
            <!-- Video Player -->
            <video id="video" controls class="video-player"></video>
            
            <!-- Loading State -->
            <div id="loading" class="player-loading">
                <div class="spinner"></div>
                <p>Loading video...</p>
            </div>
            
            <!-- Error State -->
            <div id="error" class="player-error" style="display: none;">
                <p>⚠️ Failed to load video. Please try again.</p>
                <button onclick="location.href='index.html'" class="btn-back">
                    Back to Gallery
                </button>
            </div>
        </div>
        
        <!-- Video Information -->
        <div class="video-details">
            <h2 id="video-title" class="video-title-large">Loading...</h2>
            <p id="video-description" class="video-description-full"></p>
            
            <!-- TODO: Expansion Point - Add related videos section -->
            <!-- TODO: Expansion Point - Add comments section -->
            <!-- TODO: Expansion Point - Add like/dislike buttons -->
        </div>
        
        <div class="actions">
            <a href="index.html" class="btn-secondary">← Back to Gallery</a>
            <!-- TODO: Expansion Point - Add "Add to Playlist" button -->
            <!-- TODO: Expansion Point - Add "Download" button -->
        </div>
    </main>
    
    <footer>
        <div class="container">
            <p>&copy; 2025 GoFlix. Built with Go & FFMPEG.</p>
        </div>
    </footer>
    
    <!-- hls.js library from CDN -->
    <script src="https://cdn.jsdelivr.net/npm/hls.js@latest"></script>
    <script src="watch.js"></script>
</body>
</html>