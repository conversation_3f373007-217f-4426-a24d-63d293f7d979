// https://www.youtube.com/@naftorium 

package main

import (
	"database/sql"
	"log"
	
	_ "modernc.org/sqlite"
	//_ "mattn/go-sqlite3" // Alternative SQLite driver (requires CGO) better performance but less portable
		// 	"I'm using modernc.org/sqlite instead of the traditional go-sqlite3 driver because it's pure Go and doesn't require CGO setup. 
		// This makes it much easier to get started, especially on Windows.
		//  The syntax is identical - just change the import!"
		// This will save your viewers a lot of headaches! 
)

var db *sql.DB

// InitDatabase creates the database file and tables if they don't exist
func InitDatabase() error {
	var err error
	db, err = sql.Open("sqlite", "./goflix.db")
	if err != nil {
		return err
	}
	
	// Test the connection
	if err = db.Ping(); err != nil {
		return err
	}
	
	// Create the videos table
	createTableSQL := `CREATE TABLE IF NOT EXISTS videos (
		id INTEGER PRIMARY KEY AUTOINCREMENT,
		title TEXT NOT NULL,
		description TEXT,
		thumbnail_path TEXT,
		source_filename TEXT NOT NULL
	);`
	
	_, err = db.Exec(createTableSQL)
	if err != nil {
		return err
	}
	
	log.Println("✅ Database initialized successfully")
	
	// TODO: Expansion Point - Add indexes for faster queries (e.g., on title for search)
	// TODO: Expansion Point - Create additional tables (users, watch_history, ratings)
	
	return nil
}

// SeedDatabase adds sample videos for testing
func SeedDatabase() error {
	// Check if we already have videos
	var count int
	err := db.QueryRow("SELECT COUNT(*) FROM videos").Scan(&count)
	if err != nil {
		return err
	}
	
	if count > 0 {
		log.Println("⏭️  Database already seeded, skipping...")
		return nil
	}
	
	// Sample videos - make sure these files exist in your /videos directory!
	sampleVideos := []Video{
		{
			Title:          "Big Buck Bunny",
			Description:    "A large and lovable rabbit deals with three tiny bullies.",
			ThumbnailPath:  "/thumbnails/bunny.jpg",
			SourceFilename: "bunny.mp4",
		},
		{
			Title:          "Sintel",
			Description:    "A lonely young woman, Sintel, helps and befriends a dragon.",
			ThumbnailPath:  "/thumbnails/sintel.jpg",
			SourceFilename: "sintel.mp4",
		},
		{
			Title:          "Tears of Steel",
			Description:    "A sci-fi short film set in a post-apocalyptic world.",
			ThumbnailPath:  "/thumbnails/tears.jpg",
			SourceFilename: "tears.mp4",
		},
	}
	
	insertSQL := `INSERT INTO videos (title, description, thumbnail_path, source_filename) 
	              VALUES (?, ?, ?, ?)`
	
	for _, video := range sampleVideos {
		_, err := db.Exec(insertSQL, video.Title, video.Description, 
		                  video.ThumbnailPath, video.SourceFilename)
		if err != nil {
			return err
		}
	}
	
	log.Println("✅ Database seeded with sample videos")
	
	// TODO: Expansion Point - Add a command-line flag to reset/reseed the database
	
	return nil
}

// GetAllVideos retrieves all videos from the database
func GetAllVideos() ([]Video, error) {
	rows, err := db.Query("SELECT id, title, description, thumbnail_path, source_filename FROM videos")
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	
	var videos []Video
	for rows.Next() {
		var video Video
		err := rows.Scan(&video.ID, &video.Title, &video.Description, 
		                 &video.ThumbnailPath, &video.SourceFilename)
		if err != nil {
			return nil, err
		}
		videos = append(videos, video)
	}
	
	// TODO: Expansion Point - Add pagination (LIMIT/OFFSET)
	// TODO: Expansion Point - Add sorting options (by date, title, popularity)
	
	return videos, nil
}

// GetVideoByID retrieves a single video by its ID
func GetVideoByID(id string) (*Video, error) {
	var video Video
	err := db.QueryRow(
		"SELECT id, title, description, thumbnail_path, source_filename FROM videos WHERE id = ?",
		id,
	).Scan(&video.ID, &video.Title, &video.Description, &video.ThumbnailPath, &video.SourceFilename)
	
	if err == sql.ErrNoRows {
		return nil, nil // Video not found
	}
	if err != nil {
		return nil, err
	}
	
	// TODO: Expansion Point - Increment view count when video is retrieved
	
	return &video, nil
}

// CloseDatabase closes the database connection
func CloseDatabase() {
	if db != nil {
		db.Close()
		log.Println("🔌 Database connection closed")
	}
}